<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preqin CRM - Company Search</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'pulse-soft': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .loading-spinner {
            border: 3px solid #e5e7eb;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 8px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hidden { display: none; }
        .table-cell-min { min-width: 120px; }
        .gradient-bg { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
        .glass-morphism { backdrop-filter: blur(16px); background: rgba(255, 255, 255, 0.95); }
        .shadow-soft { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03); }
        .hover-lift { transition: all 0.2s ease; }
        .hover-lift:hover { transform: translateY(-2px); box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1); }
        .data-row:hover { background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%); }
        .contact-card { transition: all 0.3s ease; border-left: 4px solid transparent; }
        .contact-card:hover { border-left-color: #3b82f6; background: #f8fafc; }
        .badge-animate { transition: all 0.2s ease; }
        .badge-animate:hover { transform: scale(1.05); }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen">
    <div class="min-h-screen p-4">
        <div class="max-w-[90%] mx-auto">
            <!-- Enhanced Header -->
            <div class="text-center mb-10 animate-fade-in">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4 shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
                    AJAIA CRM Intelligence
                </h1>
                <!-- <p class="text-gray-600 text-lg max-w-2xl mx-auto">
                    Advanced company search with real-time data integration and comprehensive contact management
                </p> -->
            </div>

            <!-- Enhanced API Credentials Form -->
            <div id="credentialsForm" class="max-w-lg mx-auto mb-10 glass-morphism rounded-2xl shadow-soft border border-white/20 p-8 animate-slide-up hidden">
                <div class="flex items-center mb-6">
                    <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900">API Authentication</h3>
                </div>
                <p class="text-gray-600 mb-6">Connect to Preqin's live data platform with your credentials</p>
                <div class="space-y-5">
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">Username</label>
                        <input type="text" id="username" placeholder="Enter your Preqin username" value="<EMAIL>"
                               class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 bg-white/70">
                    </div>
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">API Key</label>
                        <input type="password" id="apikey" placeholder="Enter your API key" value="e37abf7ce24f41eabde0accd8722bfbb"
                               class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 bg-white/70">
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="saveCredentials()"
                                class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-4 rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 font-semibold shadow-lg hover-lift">
                            Connect to Preqin
                        </button>
                    </div>
                </div>
            </div>

            <!-- Enhanced Search Bar -->
            <div class="mb-10">
                <div class="max-w-3xl mx-auto relative">
                    <div class="relative glass-morphism rounded-2xl shadow-soft border border-white/20 p-1 hover-lift">
                        <input type="text" id="searchQuery" placeholder="Search companies (e.g., BlackRock, KKR, Apollo, Goldman Sachs)"
                               class="w-full px-6 py-4 pl-14 pr-48 border-0 rounded-xl focus:ring-2 focus:ring-blue-500 outline-none text-lg bg-transparent placeholder-gray-500"
                               onkeypress="handleKeyPress(event)">
                        <svg class="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-2">
                            <button onclick="showCredentials()"
                                    class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-all duration-200 text-sm font-medium shadow-md">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Settings
                            </button>
                            <button onclick="handleSearch()" id="searchBtn"
                                    class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-2 rounded-lg hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-semibold shadow-lg">
                                Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Error Message -->
            <div id="errorMessage" class="max-w-2xl mx-auto mb-8 p-4 bg-red-50 border border-red-200 rounded-xl hidden animate-slide-up">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-red-700 font-medium" id="errorText"></p>
                </div>
            </div>

            <!-- Enhanced Loading State -->
            <div id="loadingState" class="text-center py-16 hidden animate-fade-in">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                    <div class="loading-spinner"></div>
                </div>
                <p class="text-lg text-gray-600 font-medium">Searching Preqin database...</p>
                <p class="text-sm text-gray-500 mt-2">Retrieving company and contact information</p>
            </div>

            <!-- Enhanced Search Results -->
            <div id="searchResults" class="hidden animate-fade-in">
                <div class="glass-morphism rounded-2xl shadow-soft border border-white/20 overflow-hidden">
                    <!-- Results Header -->
                    <!-- <div class="gradient-bg px-8 py-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-2xl font-bold mb-2">Company Intelligence Report</h2>
                                <p class="text-blue-100">Comprehensive view of company data and key contacts</p>
                            </div>
                            <div class="flex space-x-3">
                                <button class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Export
                                </button>
                                <button class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                    </svg>
                                    Share
                                </button>
                            </div>
                        </div>
                    </div> -->

                    <div class="grid grid-cols-1 xl:grid-cols-3 gap-0">
                        <!-- Company Overview Accordion -->
                        <div class="xl:col-span-1 bg-white border-r border-gray-100">
                            <div class="bg-blue-50 border-b border-blue-100 px-6 py-4 cursor-pointer hover:bg-blue-100 transition-colors duration-200" onclick="toggleAccordion('companyProfile')">
                                <h3 class="text-lg font-bold text-blue-900 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                        Company Profile
                                    </div>
                                    <svg id="companyProfileChevron" class="w-5 h-5 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </h3>
                            </div>
                            <div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0" id="companyProfile">
                                <div class="p-6 space-y-1" id="accountDetails">
                                    <!-- Account details will be populated here -->
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information Accordion -->
                        <div class="xl:col-span-2 bg-white">
                            <div class="bg-green-50 border-b border-green-100 px-6 py-4 cursor-pointer hover:bg-green-100 transition-colors duration-200" onclick="toggleAccordion('contactIntelligence')">
                                <h3 class="text-lg font-bold text-green-900 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                        Contact Intelligence
                                    </div>
                                    <svg id="contactIntelligenceChevron" class="w-5 h-5 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </h3>
                            </div>
                            <div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0" id="contactIntelligence">
                                <div class="p-6">
                                    <div class="overflow-x-auto">
                                        <table class="w-full text-sm" id="contactsTable">
                                            <!-- Contacts table will be populated here -->
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Footer -->
                    <div class="px-8 py-4 bg-gray-50 border-t border-gray-100">
                        <div class="flex items-center justify-between text-sm">
                            <div class="flex items-center text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Data sourced from Preqin's live API platform
                            </div>
                            <div class="text-gray-500">
                                Last updated: <span class="font-medium" id="lastUpdated"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced API Information -->
            <div id="apiInfo" class="max-w-4xl mx-auto glass-morphism rounded-2xl shadow-soft border border-white/20 p-8 mt-10">
                <div class="text-center mb-6">
                    <div class="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Preqin API Integration</h3>
                    <p class="text-gray-600">Real-time access to institutional investment data</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <h4 class="font-semibold text-gray-900">Data Sources</h4>
                        <ul class="space-y-2 text-sm text-gray-700">
                            <li class="flex items-center">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                Fund Managers & Investment Firms
                            </li>
                            <li class="flex items-center">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                Institutional Investors
                            </li>
                            <li class="flex items-center">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                Contact Information & Profiles
                            </li>
                            <li class="flex items-center">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                Asset Allocation Data
                            </li>
                        </ul>
                    </div>

                    <div class="space-y-3">
                        <h4 class="font-semibold text-gray-900">Technical Details</h4>
                        <ul class="space-y-2 text-sm text-gray-700">
                            <li class="flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                Rate limited: 10 requests/second
                            </li>
                            <li class="flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                Live data synchronization
                            </li>
                            <li class="flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                Secure authentication required
                            </li>
                            <li class="flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                Multi-asset class coverage
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-xl">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-amber-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.98-.833-2.75 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-amber-800">Important Notice</p>
                            <p class="text-xs text-amber-700 mt-1">
                                This interface connects directly to Preqin's production API. Please ensure you have proper access credentials and stay within rate limits to avoid service interruption.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isAuthenticated = false;
        let pendingCompanyName = null;

        function showCredentials() {
            document.getElementById('credentialsForm').classList.remove('hidden');
            document.getElementById('credentialsForm').scrollIntoView({ behavior: 'smooth' });
        }

        function hideCredentials() {
            document.getElementById('credentialsForm').classList.add('hidden');
        }

        function showError(message) {
            document.getElementById('errorText').textContent = message;
            document.getElementById('errorMessage').classList.remove('hidden');
            document.getElementById('errorMessage').scrollIntoView({ behavior: 'smooth' });
        }

        function hideError() {
            document.getElementById('errorMessage').classList.add('hidden');
        }

        function showLoading() {
            document.getElementById('loadingState').classList.remove('hidden');
            document.getElementById('searchBtn').disabled = true;
            document.getElementById('searchBtn').innerHTML = '<div class="loading-spinner"></div>Searching...';
        }

        function hideLoading() {
            document.getElementById('loadingState').classList.add('hidden');
            document.getElementById('searchBtn').disabled = false;
            document.getElementById('searchBtn').textContent = 'Search';
        }

        function toggleAccordion(accordionId) {
            const accordion = document.getElementById(accordionId);
            const chevron = document.getElementById(accordionId + 'Chevron');

            if (accordion.style.maxHeight && accordion.style.maxHeight !== '0px') {
                // Close accordion
                accordion.style.maxHeight = '0px';
                chevron.style.transform = 'rotate(0deg)';
            } else {
                // Open accordion
                accordion.style.maxHeight = accordion.scrollHeight + 'px';
                chevron.style.transform = 'rotate(180deg)';
            }
        }

        async function saveCredentials() {
            const username = document.getElementById('username').value;
            const apikey = document.getElementById('apikey').value;

            if (!username || !apikey) {
                showError('Please enter both username and API key');
                return;
            }

            hideError();
            showLoading();

            try {
                const response = await fetch('http://127.0.0.1:8000/api/authenticate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, apikey })
                });

                const data = await response.json();

                if (data.success) {
                    isAuthenticated = true;
                    hideCredentials();

                    // If there's a pending company name from CRM, search for it now
                    if (pendingCompanyName) {
                        document.getElementById('searchQuery').value = pendingCompanyName;
                        pendingCompanyName = null;
                        // Trigger search button click after authentication is complete
                        setTimeout(() => {
                            document.getElementById('searchBtn').click();
                        }, 100);
                    } else {
                        document.getElementById('searchQuery').focus();
                    }
                } else {
                    showError(data.error || 'Authentication failed');
                }
            } catch (error) {
                showError('Failed to connect to server');
            } finally {
                hideLoading();
            }
        }

        function skipCredentials() {
            hideCredentials();
            document.getElementById('searchQuery').focus();
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                handleSearch();
            }
        }

        async function handleSearch() {
            const query = document.getElementById('searchQuery').value.trim();

            if (!query) {
                showError('Please enter a company name');
                return;
            }

            if (!isAuthenticated) {
                showError('Please enter your Preqin API credentials first');
                showCredentials();
                return;
            }

            hideError();
            showLoading();
            document.getElementById('searchResults').classList.add('hidden');
            document.getElementById('apiInfo').classList.add('hidden');

            try {
                const response = await fetch('http://127.0.0.1:8000/api/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query })
                });

                const result = await response.json();

                if (result.success) {
                    displayResults(result.data);
                } else {
                    showError(result.error || 'Search failed');
                }
            } catch (error) {
                showError('Failed to search companies');
            } finally {
                hideLoading();
            }
        }

        function displayResults(data) {
            const { company, contacts, addresses, account } = data;

            // Update timestamp
            document.getElementById('lastUpdated').textContent = new Date().toLocaleString();

            // Enhanced account details with better styling
            const accountDetailsHTML = `
                ${[
                    { label: 'CRM Source', value: 'Preqin', highlight: true },
                    { label: 'Company Name', value: company.name, highlight: true },
                    { label: 'Rank', value: 'N/A' },
                    { label: 'Type', value: company.type, highlight: true },
                    { label: 'Subtype', value: company.type },
                    { label: 'Direct Bps ($MM)', value: 'N/A' },
                    { label: 'Fund Bps ($MM)', value: 'N/A' },
                    { label: 'AUM ($B)', value: company.aum, highlight: true },
                    { label: 'Comments', value: 'N/A' },
                    { label: 'Main Phone', value: company.phone || 'N/A', link: company.phone ? `tel:${company.phone}` : null },
                    { label: 'Street 1', value: company.address || 'N/A' },
                    { label: 'Street 2', value: 'N/A' },
                    { label: 'Cross Street', value: 'N/A' },
                    { label: 'City', value: company.city_full || 'N/A' },
                    { label: 'State', value: company.state || 'N/A' },
                    { label: 'Postal Code', value: company.zipCode || 'N/A' },
                    { label: 'Country', value: company.country || 'N/A' },
                    { label: 'Region', value: company.region || 'N/A' },
                    { label: 'Website', value: company.website || 'N/A', link: company.website },
                    { label: 'Consultant', value: company.generalConsultant || 'N/A' }
                ].map(item => `
                    <div class="flex justify-between items-center py-3 border-b border-gray-100 data-row group">
                        <span class="font-medium text-gray-700 text-sm">${item.label}</span>
                        <span class="text-right ${item.highlight ? 'font-semibold text-gray-900' : 'text-gray-600'} text-sm">
                            ${item.link ? `<a href="${item.link}" class="text-blue-600 hover:text-blue-800 transition-colors" ${item.link.startsWith('http') ? 'target="_blank" rel="noopener"' : ''}>${item.value}</a>` : item.value}
                        </span>
                    </div>
                `).join('')}
            `;

            document.getElementById('accountDetails').innerHTML = accountDetailsHTML;

            // Enhanced contacts table with modern styling
            const assetClasses = ['PE', 'PD', 'RE', 'NR', 'INF'];
            const assetClassColors = {
                'PE': 'bg-blue-100 text-blue-800 border-blue-200',
                'PD': 'bg-green-100 text-green-800 border-green-200',
                'RE': 'bg-purple-100 text-purple-800 border-purple-200',
                'NR': 'bg-orange-100 text-orange-800 border-orange-200',
                'INF': 'bg-indigo-100 text-indigo-800 border-indigo-200'
            };

            const contactsTableHTML = `
                <thead>
                    <tr class="bg-gray-50 border-b-2 border-gray-200">
                        <th class="text-left py-4 px-4 font-semibold text-gray-700 text-sm">Contact Field</th>
                        ${Array(5).fill(0).map((_, index) =>
                            `<th class="text-left py-4 px-3 font-semibold text-gray-700 text-sm table-cell-min">
                                <div class="flex flex-col">
                                    <span class="mb-1">Contact #${index + 1}</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${assetClassColors[assetClasses[index]]} border">
                                        ${assetClasses[index]}
                                    </span>
                                </div>
                            </th>`
                        ).join('')}
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-100">
                    ${[
                        { field: 'CRM Source', getValue: () => 'Preqin', highlight: true },
                        { field: 'Dear', getValue: () => 'N/A' },
                        { field: 'Salutation', getValue: () => 'N/A' },
                        { field: 'First Name', getValue: (contact) => contact?.firstName !== 'N/A' ? contact.firstName : 'N/A', highlight: true },
                        { field: 'Middle Name', getValue: () => 'N/A' },
                        { field: 'Last Name', getValue: (contact) => contact?.lastName !== 'N/A' ? contact.lastName : 'N/A', highlight: true },
                        { field: 'Suffix', getValue: () => 'N/A' },
                        { field: 'Job Title', getValue: (contact) => contact?.title !== 'N/A' ? contact.title : 'N/A', highlight: true },
                        { field: 'Phone (W)', getValue: (contact) => contact?.phone !== 'N/A' ? contact.phone : 'N/A', link: true },
                        { field: 'Mobile', getValue: () => 'N/A' },
                        { field: 'Email (W)', getValue: (contact) => contact?.email !== 'N/A' ? contact.email : 'N/A', email: true },
                        { field: 'Contact ID', getValue: (contact) => contact?.id !== 'N/A' ? contact.id : 'N/A' },
                        { field: 'Title/Prefix', getValue: (contact) => contact?.titlePrefix !== 'N/A' ? contact.titlePrefix : 'N/A' },
                        { field: 'LinkedIn', getValue: (contact) => contact?.linkedIn !== 'N/A' ? contact.linkedIn : 'N/A', linkedin: true },
                        { field: 'Asset Classes', getValue: (contact, index) => {
                            if (!contact || contact.assetClass === 'N/A') return 'N/A';
                            if (contact.assetClass.includes(assetClasses[index])) {
                                return assetClasses[index];
                            } else if (index === 0) {
                                return contact.assetClass;
                            }
                            return 'N/A';
                        }, badge: true },
                        { field: 'City', getValue: (contact) => contact?.location !== 'N/A' ? contact.location : 'N/A' },
                        { field: 'State', getValue: (contact) => contact?.state !== 'N/A' ? contact.state : 'N/A' },
                        { field: 'Zip Code', getValue: (contact) => contact?.zipCode !== 'N/A' ? contact.zipCode : 'N/A' },
                        { field: 'Country', getValue: (contact) => contact?.country !== 'N/A' ? contact.country : 'N/A' },
                        { field: 'Firm Type', getValue: (contact) => contact?.firmType !== 'N/A' ? contact.firmType : 'N/A' }
                    ].map(row => `
                        <tr class="hover:bg-gray-50 transition-colors duration-150">
                            <td class="py-3 px-4 font-medium text-gray-700 text-sm bg-gray-25">${row.field}</td>
                            ${Array(5).fill(0).map((_, index) => {
                                const contact = contacts[index];
                                const value = row.getValue(contact, index);
                                let cellContent = value;

                                if (row.email && value !== 'N/A') {
                                    cellContent = `<a href="mailto:${value}" class="text-blue-600 hover:text-blue-800 transition-colors font-medium">${value}</a>`;
                                } else if (row.link && value !== 'N/A') {
                                    cellContent = `<a href="tel:${value}" class="text-blue-600 hover:text-blue-800 transition-colors">${value}</a>`;
                                } else if (row.linkedin && value !== 'N/A') {
                                    cellContent = `<a href="https://${value}" target="_blank" class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors font-medium">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                        </svg>
                                        View Profile
                                    </a>`;
                                } else if (row.badge && value !== 'N/A') {
                                    const badgeClass = assetClassColors[value] || 'bg-gray-100 text-gray-800 border-gray-200';
                                    cellContent = `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${badgeClass} border badge-animate">${value}</span>`;
                                }

                                const cellClass = row.highlight && value !== 'N/A' ? 'font-semibold text-gray-900' : 'text-gray-600';
                                return `<td class="py-3 px-3 text-sm ${cellClass}">${cellContent}</td>`;
                            }).join('')}
                        </tr>
                    `).join('')}
                </tbody>
            `;

            document.getElementById('contactsTable').innerHTML = contactsTableHTML;
            document.getElementById('searchResults').classList.remove('hidden');
            document.getElementById('searchResults').scrollIntoView({ behavior: 'smooth' });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-connect with prefilled credentials
            setTimeout(() => {
                const connectBtn = document.querySelector('button[onclick="saveCredentials()"]');
                if (connectBtn) {
                    connectBtn.click();
                }
            }, 500);

            window.addEventListener('message', function (event) {
                // Verify the sender origin for security
                if (event.origin !== "https://champlain.crm.dynamics.com") return;

                // Access the company name
                const companyName = event.data.companyName;
                console.log('Received company name:', companyName);

                if (isAuthenticated) {
                    // If already authenticated, search immediately
                    document.getElementById("searchQuery").value = companyName;
                    document.getElementById("searchBtn").click();
                } else {
                    // If not authenticated yet, store the company name for later
                    pendingCompanyName = companyName;
                    console.log('Authentication pending, storing company name for later search');
                }
            });

            // Focus on search input when credentials are hidden
            const credForm = document.getElementById('credentialsForm');
            if (credForm.classList.contains('hidden')) {
                document.getElementById('searchQuery').focus();
            }

            // Add some interactive effects only to search bar with minimal movement
            document.addEventListener('mousemove', function(e) {
                const searchBar = document.querySelector('.max-w-3xl .glass-morphism');
                if (searchBar) {
                    const rect = searchBar.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
                        // Very minimal movement - reduced from /50 to /200
                        searchBar.style.transform = `perspective(1000px) rotateX(${(y - rect.height / 2) / 200}deg) rotateY(${(x - rect.width / 2) / 200}deg)`;
                    } else {
                        searchBar.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg)';
                    }
                }
            });
        });
    </script>
</body>
</html>